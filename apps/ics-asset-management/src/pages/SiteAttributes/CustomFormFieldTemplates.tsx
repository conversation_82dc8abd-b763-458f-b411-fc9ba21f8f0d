import React, { useState } from 'react';
import {
  ArrayFieldTemplateProps,
  ArrayFieldTemplateItemType,
} from '@rjsf/utils';
import {
  ExpandLess,
  ExpandMore,
  NorthOutlined,
  SouthOutlined,
  DeleteOutlineOutlined,
} from '@mui/icons-material';
import { IconButton, Box } from '@mui/material';
import './CustomFormFieldTemplates.css';


// Array field item template with expand/collapse functionality
const CustomArrayFieldItemTemplate: React.FC<ArrayFieldTemplateItemType> = ({
  children,
  disabled,
  hasRemove,
  hasMoveDown,
  hasMoveUp,
  index,
  onDropIndexClick,
  onReorderClick,
  readonly,
  className,
}) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(true);

  const buttonStyle = {
    minWidth: 'auto',
    padding: '4px',
  };

  // Create array buttons with consistent styling and always visible behavior
  const removeButton = (
    <IconButton
      style={buttonStyle}
      onClick={hasRemove ? onDropIndexClick(index) : undefined}
      disabled={disabled || readonly || !hasRemove}
      size='small'
      title='Remove'
    >
      <DeleteOutlineOutlined
        fontSize='small'
        sx={{
          color: disabled || readonly || !hasRemove ? '#48464A' : '#48464A'
        }}
      />
    </IconButton>
  );

  const moveUpButton = (
    <IconButton
      style={buttonStyle}
      onClick={hasMoveUp ? onReorderClick(index, index - 1) : undefined}
      disabled={disabled || readonly || !hasMoveUp}
      size='small'
      title='Move Up'
    >
      <NorthOutlined
        fontSize='small'
        sx={{
          color: disabled || readonly || !hasMoveUp ? '#48464A' : '#27725B',
        }}
      />
    </IconButton>
  );

  const moveDownButton = (
    <IconButton
      style={buttonStyle}
      onClick={hasMoveDown ? onReorderClick(index, index + 1) : undefined}
      disabled={disabled || readonly || !hasMoveDown}
      size='small'
      title='Move Down'
    >
      <SouthOutlined
        fontSize='small'
        sx={{
          color: disabled || readonly || !hasMoveDown ? '#48464A' : '#27725B',
        }}
      />
    </IconButton>
  );

  return (
    <Box
      className={className}
      style={{
        backgroundColor: 'white',
        padding: '16px',
        marginBottom: '8px',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }}
    >
      {/* Array item header with buttons and expand/collapse */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center', // Changed from 'flex-start' to 'center' for inline alignment
          minHeight: '32px',
          marginBottom: isExpanded ? '8px' : '0px',
        }}
      >
        {/* Left side: Array buttons (MoveUp, MoveDown, Remove) */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          {moveUpButton}
          {moveDownButton}
          {removeButton}
        </div>

        {/* Right side: Expand/collapse icon */}
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {!isExpanded ? (
            <ExpandMore
              fontSize='small'
              color='action'
              onClick={() => setIsExpanded(prev => !prev)}
              style={{ cursor: 'pointer' }}
            />
          ) : (
            <ExpandLess
              fontSize='small'
              color='action'
              onClick={() => setIsExpanded(prev => !prev)}
              style={{ cursor: 'pointer' }}
            />
          )}
        </div>
      </div>

      {/* Array item content - only show when expanded */}
      {isExpanded && (
        <Box style={{ paddingTop: '8px' }}>
          {children}
        </Box>
      )}
    </Box>
  );
};

// Array field template
const CustomArrayFieldTemplate: React.FC<ArrayFieldTemplateProps> = ({
  items,
  schema: _schema,
}) => (
    <Box>
      {/* Render array items */}
      {items &&
        items.map(itemProps => (
          <CustomArrayFieldItemTemplate key={itemProps.key} {...itemProps} />
        ))}
    </Box>
  );

export {
  CustomArrayFieldTemplate,
  CustomArrayFieldItemTemplate,
};