import React, { useState, createContext, useContext, useMemo } from 'react';
import {
  ObjectFieldTemplateProps,
  ArrayFieldTemplateProps,
  ArrayFieldTemplateItemType,
} from '@rjsf/utils';
import {
  ExpandLess,
  ExpandMore,
  NorthOutlined,
  SouthOutlined,
  DeleteOutlineOutlined,
} from '@mui/icons-material';
import { IconButton, Box } from '@mui/material';
import './CustomFormFieldTemplates.css';

// Context for passing array item buttons to nested components
const ArrayItemContext = createContext<{
  removeButton?: React.ReactNode;
  moveUpButton?: React.ReactNode;
  moveDownButton?: React.ReactNode;
}>({});

const CustomObjectFieldTemplate: React.FC<ObjectFieldTemplateProps> = ({
  idSchema,
  properties,
}) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(true);
  const { removeButton, moveUpButton, moveDownButton } = useContext(ArrayItemContext);

  if (!properties.length) return null;

  const hasArrayItemButtons = moveUpButton || moveDownButton || removeButton;

  return (
    <>
      <span id={idSchema.$id} />

      {/* Array item buttons and expand/collapse for array fields */}
      {hasArrayItemButtons && (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            minHeight: '32px',
            marginBottom: '8px',
          }}
        >
          {/* Left side: Array buttons (MoveUp, MoveDown, Remove) */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            {moveUpButton}
            {moveDownButton}
            {removeButton}
          </div>

          {/* Right side: Expand/collapse icon */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {!isExpanded ? (
              <ExpandMore
                fontSize='small'
                color='action'
                onClick={() => setIsExpanded(prev => !prev)}
                style={{ cursor: 'pointer' }}
              />
            ) : (
              <ExpandLess
                fontSize='small'
                color='action'
                onClick={() => setIsExpanded(prev => !prev)}
                style={{ cursor: 'pointer' }}
              />
            )}
          </div>
        </div>
      )}

      {isExpanded && (
        <div className=''>
          {properties.map(items => (
            <div key={items.name} className='property-wrapper field-content'>
              {items.content}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

// Array field item template with expand/collapse functionality
const CustomArrayFieldItemTemplate: React.FC<ArrayFieldTemplateItemType> = ({
  children,
  disabled,
  hasRemove,
  hasMoveDown,
  hasMoveUp,
  index,
  onDropIndexClick,
  onReorderClick,
  readonly,
  className,
}) => {
  const buttonStyle = {
    minWidth: 'auto',
    padding: '4px',
  };

  // Create array buttons with consistent styling and always visible behavior
  const removeButton = (
    <IconButton
      style={buttonStyle}
      onClick={hasRemove ? onDropIndexClick(index) : undefined}
      disabled={disabled || readonly || !hasRemove}
      size='small'
      title='Remove'
    >
      <DeleteOutlineOutlined
        fontSize='small'
        sx={{
          color: disabled || readonly || !hasRemove ? '#48464A' : '#48464A'
        }}
      />
    </IconButton>
  );

  const moveUpButton = (
    <IconButton
      style={buttonStyle}
      onClick={hasMoveUp ? onReorderClick(index, index - 1) : undefined}
      disabled={disabled || readonly || !hasMoveUp}
      size='small'
      title='Move Up'
    >
      <NorthOutlined
        fontSize='small'
        sx={{
          color: disabled || readonly || !hasMoveUp ? '#48464A' : '#27725B',
        }}
      />
    </IconButton>
  );

  const moveDownButton = (
    <IconButton
      style={buttonStyle}
      onClick={hasMoveDown ? onReorderClick(index, index + 1) : undefined}
      disabled={disabled || readonly || !hasMoveDown}
      size='small'
      title='Move Down'
    >
      <SouthOutlined
        fontSize='small'
        sx={{
          color: disabled || readonly || !hasMoveDown ? '#48464A' : '#27725B',
        }}
      />
    </IconButton>
  );

  const arrayItemContextValue = useMemo(
    () => ({
      removeButton,
      moveUpButton,
      moveDownButton,
    }),
    [removeButton, moveUpButton, moveDownButton]
  );

  return (
    <ArrayItemContext.Provider value={arrayItemContextValue}>
      <Box
        style={{
          display: 'flex',
          alignItems: 'flex-start',
          gap: '8px',
          padding: '8px',
          position: 'relative',
        }}
        className={className}
      >
        <Box style={{ flex: 1 }}>{children}</Box>
      </Box>
    </ArrayItemContext.Provider>
  );
};

// Array field template
const CustomArrayFieldTemplate: React.FC<ArrayFieldTemplateProps> = ({
  items,
  schema: _schema,
}) => (
    <Box>
      {/* Render array items */}
      {items &&
        items.map(itemProps => (
          <CustomArrayFieldItemTemplate key={itemProps.key} {...itemProps} />
        ))}
    </Box>
  );

export {
  CustomObjectFieldTemplate,
  CustomArrayFieldTemplate,
  CustomArrayFieldItemTemplate,
};