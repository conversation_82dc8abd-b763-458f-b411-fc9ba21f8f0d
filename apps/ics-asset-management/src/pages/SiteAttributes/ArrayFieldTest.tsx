import React, { useState } from 'react';
import Form from '@rjsf/mui';
import validator from '@rjsf/validator-ajv8';
import { RJSFSchema } from '@rjsf/utils';
import { Box, Typography } from '@mui/material';
import { 
  CustomObjectFieldTemplate,
  CustomArrayFieldTemplate,
  CustomArrayFieldItemTemplate,
} from './CustomFormFieldTemplates';

// Test schema with array fields
const testSchema: RJSFSchema = {
  type: 'object',
  properties: {
    simpleArray: {
      type: 'array',
      title: 'Simple Array',
      items: {
        type: 'string',
        title: 'Item'
      }
    },
    objectArray: {
      type: 'array',
      title: 'Object Array',
      items: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            title: 'Name'
          },
          value: {
            type: 'number',
            title: 'Value'
          },
          enabled: {
            type: 'boolean',
            title: 'Enabled'
          }
        }
      }
    },
    nestedArray: {
      type: 'array',
      title: 'Nested Array',
      items: {
        type: 'object',
        properties: {
          category: {
            type: 'string',
            title: 'Category'
          },
          items: {
            type: 'array',
            title: 'Sub Items',
            items: {
              type: 'string',
              title: 'Sub Item'
            }
          }
        }
      }
    }
  }
};

const testData = {
  simpleArray: ['Item 1', 'Item 2'],
  objectArray: [
    { name: 'Object 1', value: 100, enabled: true },
    { name: 'Object 2', value: 200, enabled: false }
  ],
  nestedArray: [
    {
      category: 'Category A',
      items: ['Sub Item 1', 'Sub Item 2']
    }
  ]
};

const ArrayFieldTest: React.FC = () => {
  const [formData, setFormData] = useState(testData);

  return (
    <Box sx={{ padding: 2 }}>
      <Typography variant="h5" gutterBottom>
        Array Field Expand/Collapse Test
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        This test demonstrates the expand/collapse functionality for array fields ONLY.
        Array items should have MoveUp, MoveDown, and Remove buttons on the left,
        with expand/collapse icons on the right. Object fields remain unchanged.
      </Typography>
      
      <Box sx={{ marginTop: 2 }}>
        <Form
          schema={testSchema}
          formData={formData}
          onChange={({ formData: newFormData }) => setFormData(newFormData)}
          validator={validator}
          templates={{
            ObjectFieldTemplate: CustomObjectFieldTemplate,
            ArrayFieldTemplate: CustomArrayFieldTemplate,
            ArrayFieldItemTemplate: CustomArrayFieldItemTemplate,
          }}
          onSubmit={() => {}}
        />
      </Box>
      
      <Box sx={{ marginTop: 2, padding: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="h6">Current Form Data:</Typography>
        <pre style={{ fontSize: '12px', overflow: 'auto' }}>
          {JSON.stringify(formData, null, 2)}
        </pre>
      </Box>
    </Box>
  );
};

export default ArrayFieldTest;
