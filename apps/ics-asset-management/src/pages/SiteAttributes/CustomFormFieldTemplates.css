.field-content {
  padding-left: 20px;
}

.card-shadow {
  display: flex;
  flex-direction: column;
  gap: 8px;
  border-radius: 8px;
  padding: 5px;
  margin-bottom: 8px;
}

.card-shadow-white {
  background-color: white;
  padding: 16px 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layout-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.property-wrapper {
  margin-bottom: 8px;
}

.form-group .disabled input {
  cursor: not-allowed;
  background-color: #f0f0f0;
  color: #626367;
}

.form-group label {
  margin-bottom: 0px;
}

.field-object,
.field-string,
.field-boolean,
.field-number,
.field-array {
  margin-bottom: 0px;
}

.form-group {
  margin-bottom: 0px;
}
